{"kind": "collectionType", "collectionName": "evaluations_answers", "info": {"singularName": "evaluations-answer", "pluralName": "evaluations-answers", "displayName": "Evaluations Answers", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"answers": {"type": "component", "repeatable": true, "component": "evaluation-questions.evaluation-questions"}, "student_name": {"type": "string"}, "parent": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "class": {"type": "relation", "relation": "oneToOne", "target": "api::class.class"}}}