'use strict';

/**
 * nursery controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::nursery.nursery', ({ strapi }) => ({
  async nurseriesStatistics(ctx) {
    try {
      // Fetch all nurseries
      const nurseries = await strapi.entityService.findMany('api::nursery.nursery', {
        populate: {
          logo: true
        },
        sort: 'createdAt:desc'
      });

      // Helper function to transform entity data to REST API format
      const transformToRestFormat = (entities) => {
        if (!entities || !Array.isArray(entities)) return [];

        return entities.map(entity => {
          const { id, ...attributes } = entity;
          return {
            id,
            attributes
          };
        });
      };

      // Process each nursery to get student statistics
      const nurseriesWithStats = await Promise.all(
        nurseries.map(async (nursery) => {
          // Get active students count for this nursery
          const activeStudentsCount = await strapi.entityService.count('api::student.student', {
            filters: {
              nursery: { id: nursery.id },
              is_active: true
            }
          });

          // Get inactive students count for this nursery
          const inactiveStudentsCount = await strapi.entityService.count('api::student.student', {
            filters: {
              nursery: { id: nursery.id },
              is_active: false
            }
          });

          // Return nursery with statistics
          const { id, ...attributes } = nursery;
          return {
            id,
            attributes: {
              ...attributes,
              active_students: activeStudentsCount,
              inactive_students: inactiveStudentsCount
            }
          };
        })
      );

      // Return data in REST API format
      ctx.send({
        data: nurseriesWithStats,
        meta: {
          count: nurseriesWithStats.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      ctx.throw(500, `Failed to fetch nurseries statistics: ${error.message}`);
    }
  },
}));
