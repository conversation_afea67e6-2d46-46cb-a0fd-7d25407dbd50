'use strict';

/**
 * evaluation controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::evaluation.evaluation', ({ strapi }) => ({
  // Function to get evaluations excluding those where parent_id and student_id combination exists in parent_ids array
  async evaluationsByParentId(ctx) {
    try {
      const { query } = ctx;

      // Extract parent_id and student_id parameters
      const parentId = ctx.params.parent_id || query.parent_id;
      const studentId = query.student_id;

      if (!parentId) {
        return ctx.badRequest('parent_id parameter is required');
      }

      if (!studentId) {
        return ctx.badRequest('student_id parameter is required');
      }

      // Extract filters from query (same as default API)
      const filters = {};

      // Handle nursery filter
      if (query.filters?.nursery?.id) {
        filters.nursery = { id: query.filters.nursery.id };
      }

      // Handle classes filter
      if (query.filters?.classes) {
        filters.classes = query.filters.classes;
      }

      // Handle date filter
      if (query.filters?.date) {
        filters.date = query.filters.date;
      }

      // Handle send_to filter
      if (query.filters?.send_to) {
        filters.send_to = query.filters.send_to;
      }

      // Handle any additional filters from query
      if (query.filters) {
        Object.keys(query.filters).forEach(key => {
          if (!['nursery', 'classes', 'date', 'send_to'].includes(key)) {
            filters[key] = query.filters[key];
          }
        });
      }

      // Build populate parameters (same as default API)
      const populate = {};

      // Handle populate from query parameters
      if (query.populate) {
        if (typeof query.populate === 'object') {
          Object.assign(populate, query.populate);
        }
      }

      // Always populate parent_ids with nested student_ids to check for parent_id and student_id existence
      populate.parent_ids = {
        populate: {
          student_ids: true
        }
      };

      // Handle sorting
      const sort = query.sort || 'createdAt:desc';

      // Build query
      const evaluationQuery = {
        filters,
        populate,
        sort,
      };

      // Add pagination if provided
      if (query.pagination) {
        evaluationQuery.pagination = query.pagination;
      }

      // Fetch all evaluations
      const evaluations = await strapi.entityService.findMany('api::evaluation.evaluation', evaluationQuery);

      // Filter out evaluations where parent_id and student_id combination exists in parent_ids array
      const filteredEvaluations = evaluations.filter(evaluation => {
        // If no parent_ids array, include the evaluation
        if (!evaluation.parent_ids || !Array.isArray(evaluation.parent_ids)) {
          return true;
        }

        // Check if parent_id and student_id combination exists in the parent_ids array
        const parentStudentExists = evaluation.parent_ids.some(parentIdObj => {
          // Check if parent_id matches
          if (!parentIdObj.parent_id || parentIdObj.parent_id.toString() !== parentId.toString()) {
            return false;
          }

          // If parent_id matches, check if student_id exists in student_ids array
          if (!parentIdObj.student_ids || !Array.isArray(parentIdObj.student_ids)) {
            return false;
          }

          // Check if student_id exists in the student_ids array
          return parentIdObj.student_ids.some(studentIdObj =>
            studentIdObj.student_id && studentIdObj.student_id.toString() === studentId.toString()
          );
        });
        // Return true if parent_id and student_id combination does NOT exist (include evaluation)
        return !parentStudentExists;
      });

      // Get total count for pagination meta
      const totalCount = filteredEvaluations.length;

      // Apply pagination to filtered results if needed
      let paginatedEvaluations = filteredEvaluations;
      let paginationMeta = {
        page: 1,
        pageSize: totalCount,
        pageCount: 1,
        total: totalCount
      };

      if (query.pagination) {
        const page = parseInt(query.pagination.page) || 1;
        const pageSize = parseInt(query.pagination.pageSize) || 25;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        paginatedEvaluations = filteredEvaluations.slice(startIndex, endIndex);

        paginationMeta = {
          page,
          pageSize,
          pageCount: Math.ceil(totalCount / pageSize),
          total: totalCount
        };
      }

      // Transform to REST API format (same as default API)
      const transformedData = paginatedEvaluations.map(evaluation => {
        const { id, parent_ids, ...attributes } = evaluation;

        // Remove parent_ids from response to match default API format
        return {
          id,
          attributes
        };
      });

      // Return data in the same format as default API
      ctx.send({
        data: transformedData,
        meta: {
          pagination: paginationMeta
        }
      });

    } catch (error) {
      ctx.throw(500, `Failed to fetch evaluations: ${error.message}`);
    }
  },
}));
