{"kind": "collectionType", "collectionName": "evaluations", "info": {"singularName": "evaluation", "pluralName": "evaluations", "displayName": "Evaluation", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"date": {"type": "string"}, "classes": {"type": "relation", "relation": "oneToMany", "target": "api::class.class"}, "send_to": {"type": "enumeration", "enum": ["all_parents", "class"]}, "questions": {"displayName": "Evaluation Questions", "type": "component", "repeatable": true, "component": "evaluation-questions.evaluation-questions"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}