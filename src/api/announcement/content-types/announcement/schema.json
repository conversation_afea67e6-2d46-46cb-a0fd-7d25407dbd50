{"kind": "collectionType", "collectionName": "announcements", "info": {"singularName": "announcement", "pluralName": "announcements", "displayName": "Announcement", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "target": {"type": "enumeration", "enum": ["admins", "teachers", "parents", "class", "all"]}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "class": {"type": "relation", "relation": "oneToOne", "target": "api::class.class"}}}