import type { Attribute, Schema } from '@strapi/strapi';

export interface AccessPermissionsAccessPermissions extends Schema.Component {
  collectionName: 'components_access_permissions_access_permissions';
  info: {
    displayName: 'Access Permissions';
    icon: 'crown';
  };
  attributes: {
    permission: Attribute.String;
  };
}

export interface ActivityNotesActivityNote extends Schema.Component {
  collectionName: 'components_activity_notes_activity_notes';
  info: {
    description: '';
    displayName: 'Activity Note';
    icon: 'file';
  };
  attributes: {
    date: Attribute.String;
    media: Attribute.Media<'images' | 'files' | 'videos' | 'audios', true>;
    note: Attribute.Text;
  };
}

export interface AttendanceAttendance extends Schema.Component {
  collectionName: 'components_attendance_attendances';
  info: {
    displayName: 'Attendance';
    icon: 'calendar';
  };
  attributes: {};
}

export interface EvaluationQuestionsEvaluationQuestions
  extends Schema.Component {
  collectionName: 'components_evaluation_questions_evaluation_questions';
  info: {
    description: '';
    displayName: 'Evaluation Questions';
    icon: 'bulletList';
  };
  attributes: {
    answer: Attribute.Text;
    question: Attribute.Text;
    rate: Attribute.Decimal;
    type: Attribute.Enumeration<['text', 'rating']>;
  };
}

export interface OnBoardingOnBoarding extends Schema.Component {
  collectionName: 'components_on_boarding_on_boardings';
  info: {
    description: '';
    displayName: 'On Boarding';
    icon: 'cog';
  };
  attributes: {
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    subtitle: Attribute.Text;
    title: Attribute.String;
  };
}

export interface PaymentMethodsPaymentMethods extends Schema.Component {
  collectionName: 'components_payment_methods_payment_methods';
  info: {
    displayName: 'Payment Methods';
    icon: 'command';
  };
  attributes: {
    etisalatCash: Attribute.String;
    instapay: Attribute.String;
    orangeCash: Attribute.String;
    vodafoneCash: Attribute.String;
    weCash: Attribute.String;
  };
}

export interface PickUpPersonsPickUpPersons extends Schema.Component {
  collectionName: 'components_pick_up_persons_pick_up_persons';
  info: {
    displayName: 'Pick Up Persons';
    icon: 'car';
  };
  attributes: {
    person: Attribute.String;
  };
}

export interface StudentExamStudentExam extends Schema.Component {
  collectionName: 'components_student_exam_student_exams';
  info: {
    displayName: 'Student Exam';
    icon: 'file';
  };
  attributes: {
    note: Attribute.Text;
    rate: Attribute.Decimal &
      Attribute.SetMinMax<
        {
          max: 5;
        },
        number
      >;
    student: Attribute.Relation<
      'student-exam.student-exam',
      'oneToOne',
      'api::student.student'
    >;
  };
}

export interface StudentIdsStudentIds extends Schema.Component {
  collectionName: 'components_student_ids_student_ids';
  info: {
    description: '';
    displayName: 'Student Ids';
    icon: 'alien';
  };
  attributes: {
    class_id: Attribute.Integer;
    student_id: Attribute.Integer;
  };
}

export interface SubscriptionSubscription extends Schema.Component {
  collectionName: 'components_subscription_subscriptions';
  info: {
    description: '';
    displayName: 'Subscription';
    icon: 'book';
  };
  attributes: {
    amount: Attribute.Integer;
    date: Attribute.String;
    is_approved: Attribute.Boolean & Attribute.DefaultTo<true>;
    is_paid: Attribute.Boolean & Attribute.DefaultTo<true>;
    payment_method: Attribute.String;
    payment_screenshot: Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'access-permissions.access-permissions': AccessPermissionsAccessPermissions;
      'activity-notes.activity-note': ActivityNotesActivityNote;
      'attendance.attendance': AttendanceAttendance;
      'evaluation-questions.evaluation-questions': EvaluationQuestionsEvaluationQuestions;
      'on-boarding.on-boarding': OnBoardingOnBoarding;
      'payment-methods.payment-methods': PaymentMethodsPaymentMethods;
      'pick-up-persons.pick-up-persons': PickUpPersonsPickUpPersons;
      'student-exam.student-exam': StudentExamStudentExam;
      'student-ids.student-ids': StudentIdsStudentIds;
      'subscription.subscription': SubscriptionSubscription;
    }
  }
}
